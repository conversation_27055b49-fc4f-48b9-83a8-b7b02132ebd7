{"format": 1, "restore": {"/Users/<USER>/Downloads/otelcustommetrics-c/MyOpenTelemetryApp/MyOpenTelemetryApp.csproj": {}}, "projects": {"/Users/<USER>/Downloads/otelcustommetrics-c/MyOpenTelemetryApp/MyOpenTelemetryApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Downloads/otelcustommetrics-c/MyOpenTelemetryApp/MyOpenTelemetryApp.csproj", "projectName": "MyOpenTelemetryApp", "projectPath": "/Users/<USER>/Downloads/otelcustommetrics-c/MyOpenTelemetryApp/MyOpenTelemetryApp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Downloads/otelcustommetrics-c/MyOpenTelemetryApp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"OpenTelemetry": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.12.0, )"}, "System.Diagnostics.DiagnosticSource": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}