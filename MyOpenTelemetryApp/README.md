
# OpenTelemetry Custom Metrics Application

This project demonstrates how to send custom metrics (<PERSON>, Gauge, and Histogram) to an OpenTelemetry Collector using .NET.

## Prerequisites

### Install .NET SDK

#### Windows
1. Download .NET 9.0 SDK from [https://dotnet.microsoft.com/download](https://dotnet.microsoft.com/download)
2. Run the installer and follow the setup wizard

#### macOS
```bash
# Using Homebrew
brew install --cask dotnet

# Or download from Microsoft
curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 9.0
```

#### Linux (Ubuntu/Debian)
```bash
# Add Microsoft package repository
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
rm packages-microsoft-prod.deb

# Install .NET SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-9.0
```

Verify installation:
```bash
dotnet --version
```

## Project Setup

### 1. Create New Project
```bash
# Create new console application
dotnet new console -n MyOpenTelemetryApp
cd MyOpenTelemetryApp
```

### 2. Add NuGet Dependencies
```bash
# Add OpenTelemetry packages
dotnet add package OpenTelemetry --version 1.12.0
dotnet add package OpenTelemetry.Exporter.OpenTelemetryProtocol --version 1.12.0
dotnet add package OpenTelemetry.Exporter.Console --version 1.12.0
dotnet add package System.Diagnostics.DiagnosticSource --version 9.0.0
```

### 3. Restore Dependencies
```bash
dotnet restore
```

### 4. Build Project
```bash
dotnet build
```

### 5. Run Application
```bash
dotnet run
```

## Project Structure

```
MyOpenTelemetryApp/
├── Program.cs                 # Main application entry point
├── MetricsService.cs         # Custom metrics implementation
├── MyOpenTelemetryApp.csproj # Project file with dependencies
└── README.md                 # This file
```

## Configuration

### OpenTelemetry Collector Endpoint
The application is configured to send metrics to an OpenTelemetry Collector at:
- **Endpoint**: `http://localhost:4318/v1/metrics`
- **Protocol**: HTTP/Protobuf

### Metrics Exported
- **Counter**: `custom.request.count` - Tracks number of requests
- **Gauge**: `custom.memory.usage` - Current memory usage
- **Histogram**: `custom.request.duration` - Request processing time distribution

## Usage

1. Start your OpenTelemetry Collector
2. Run the application: `dotnet run`
3. The application will send sample metrics to the collector
4. Check your collector logs to verify metrics are received

## OpenTelemetry Collector Configuration

Example collector configuration (`otel-collector.yaml`):

```yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

exporters:
  logging:
    loglevel: debug
  # Add your preferred exporter (Prometheus, Jaeger, etc.)

service:
  pipelines:
    metrics:
      receivers: [otlp]
      exporters: [logging]
```

## Troubleshooting

- Ensure OpenTelemetry Collector is running on `localhost:4318`
- Check firewall settings if connection fails
- Verify all NuGet packages are properly restored
- Check collector logs for any processing errors

