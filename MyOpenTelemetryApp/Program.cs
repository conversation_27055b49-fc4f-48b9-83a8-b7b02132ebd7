using System;
using System.Threading;
using System.Diagnostics.Metrics;
using OpenTelemetry;
using OpenTelemetry.Metrics;

class Program
{
    static void Main(string[] args)
    {
        using var meterProvider = Sdk.CreateMeterProviderBuilder()
            .AddMeter("MyCustomMeter")
            .AddOtlpExporter(options =>
            {
                options.Endpoint = new Uri("https://ingress.ap1.coralogix.com/v1/metrics"); // Replace with your region
                options.Headers = "Authorization=Bearer YOUR_API_KEY"; // Replace with your API key
                options.Protocol = OpenTelemetry.Exporter.OtlpExportProtocol.HttpProtobuf; // Use HTTP for Coralogix
            })
            .Build();

        var meter = new Meter("MyCustomMeter", "1.0.0");
        var customCounter = meter.CreateCounter<long>("my.counter", description: "A custom counter for demonstration");

        customCounter.Add(1, new KeyValuePair<string, object?>("tag", "value"));

        Console.WriteLine("Hello, World!");
        Console.WriteLine("Current Date and Time: " + DateTime.Now.ToString());

        meterProvider.ForceFlush(5000);
        Thread.Sleep(1000);
    }
}